package persistence

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

func (r *UserRepository) CreateUserClient(ctx context.Context, dB database.DBI, userClient *models.UserClient) error {
	return dB.GetTx().Create(userClient).Error
}

func enrichUserClientQueryWithWhere(query *gorm.DB, where models.UserClientWhere) {
	if where.UserID != "" {
		query.Where("user_id = ?", where.UserID)
	} // UserID

	if len(where.UserIDs) > 0 {
		query.Where("user_id IN ?", where.UserIDs)
	} // UserIDs

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.UnencryptedPIN != "" {
		query.Where("unencrypted_pin = ?", where.UnencryptedPIN)
	} // UnencryptedPIN

	if where.PermissionGroupID != "" {
		query.Where("permission_group_id = ?", where.PermissionGroupID)
	} // PermissionGroupID
}

func enrichUserClientQueryWithPreload(query *gorm.DB, where models.UserClientPreload) {
	if where.Client {
		query.Preload("Client")
	} // Client
	if where.User {
		query.Preload("User")
	} // User
}

func (r *UserRepository) GetUserClients(ctx context.Context, dB database.DBI, condition models.UserClientCondition) ([]models.UserClient, error) {
	userClients := []models.UserClient{}
	query := dB.GetOrm().Model(&userClients)

	enrichUserClientQueryWithWhere(query, condition.Where)
	enrichUserClientQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.Find(&userClients).Error
	if err != nil {
		return nil, err
	}
	return userClients, nil
}

func (r *UserRepository) GetUserClient(ctx context.Context, dB database.DBI, condition models.UserClientCondition) (*models.UserClient, error) {
	userClients := &models.UserClient{}
	query := dB.GetOrm().Model(&userClients)

	enrichUserClientQueryWithWhere(query, condition.Where)
	enrichUserClientQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.First(&userClients).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("USER_CLIENT")
		}
		return nil, err
	}
	return userClients, nil
}
