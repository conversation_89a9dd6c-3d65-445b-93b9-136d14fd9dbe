package handler

import (
	"assetfindr/internal/app/user-identity/constants"
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	UserUseCase       *usecase.UserUseCase
	PermissionUseCase *usecase.PermissionUseCase
}

func NewUserHandler(
	userUseCase *usecase.UserUseCase,
	permissionUseCase *usecase.PermissionUseCase,
) *UserHandler {
	return &UserHandler{
		UserUseCase:       userUseCase,
		PermissionUseCase: permissionUseCase,
	}
}

func (h *UserHandler) UserRegister(c *gin.Context) {
	ctx := c.Request.Context()
	var requestData dtos.UserRegisterRequest
	if err := c.BindJ<PERSON>(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	err := requestData.ValidateUserRegisterRequest()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response, err := h.UserUseCase.UserRegister(ctx, &requestData)
	if err != nil {
		if err == errorhandler.ErrUserAlreadyRegistered {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	helpers.SetJwtTokenCookie(c, response.JwtToken)
	c.JSON(http.StatusCreated, response)
}

func (h *UserHandler) UserLoginWithFirebaseToken(c *gin.Context) {
	ctx := c.Request.Context()
	var requestData dtos.UserLoginWithFirebaseRequest
	if err := c.BindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	err := requestData.ValidateUserLoginWithFirebaseRequest()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response, err := h.UserUseCase.UserLoginWithFirebaseToken(ctx, &requestData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	helpers.SetJwtTokenCookie(c, response.JwtToken)
	c.JSON(http.StatusCreated, response)

}

func (h *UserHandler) Logout(c *gin.Context) {
	var req dtos.LogoutReq
	err := c.BindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	err = h.UserUseCase.Logout(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	helpers.UnsetJwtTokenCookie(c)
	c.String(http.StatusOK, "Logged out successfully")

}

func (h *UserHandler) Auth(c *gin.Context) {

	ctx := c.Request.Context()
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, claim)

}

func (h *UserHandler) CreateUserDevice(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateUserDeviceReq
	err := c.BindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	resp, err := h.UserUseCase.CreateUserDevice(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *UserHandler) CreateUser(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateUserReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := req.Vaildate()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.UserUseCase.CreateUser(ctx, &req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, response)
}

func (h *UserHandler) GetUserList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetUserListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.UserUseCase.GetUserList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *UserHandler) GetUser(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")

	resp, err := h.UserUseCase.GetUser(ctx, ID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *UserHandler) UpdateUser(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.UpdateUserReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	id := c.Param("id")
	response, err := h.UserUseCase.UpdateUser(ctx, id, &req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *UserHandler) UpdateUserEmail(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.ChangeUserEmailReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.UserUseCase.UpdateUserEmail(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *UserHandler) DeleteUser(c *gin.Context) {
	id := c.Param("id")
	resp, err := h.UserUseCase.DeleteUser(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *UserHandler) ChangeUserPassword(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.ChangeUserPasswordReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.UserUseCase.ChangeUserPassword(ctx, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *UserHandler) CheckClientAliasAvailibility(c *gin.Context) {
	clientAlias := c.Query("client_alias")
	res, err := h.UserUseCase.CheckClientAliasAvailibility(c.Request.Context(), clientAlias)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, res)
}

func (h *UserHandler) RegisterAFChallenge(c *gin.Context) {
	var req dtos.RegisterAFChallange
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	// Only predefined data for create user
	tokenClaim := authhelpers.JwtTokenClaims{
		UserID:           constants.AF_CHALLANGE_USER_CREATOR_ID,
		LoggedInClientID: constants.AF_CHALLANGE_CLIENT_ID,
	}

	newCtx := authhelpers.SetClaimToReqCtx(c.Request.Context(), tokenClaim)
	c.Request = c.Request.WithContext(newCtx)

	newReq := dtos.CreateUserReq{
		FirstName:         req.FirstName,
		LastName:          req.LastName,
		ReferenceID:       req.CompanyName,
		Email:             "cs+" + req.PhoneNumber + "@assetfindr.com",
		Password:          constants.AF_CHALLANGE_STATIC_PASSWORD,
		PhoneNumber:       req.PhoneNumber,
		ReferenceRole:     req.RelatedIndustry,
		PermissionGroupID: constants.AF_CHALLANGE_PERMISSION_GROUP_ID,
		Photo:             req.Photo,
		DepartmentID:      constants.AF_CHALLANGE_DEPARTMENT_ID,
		NewDepartment:     req.NewDepartment,
		Remark:            req.Email + " - " + req.ReferenceRole,
	}

	response, err := h.UserUseCase.CreateUser(newCtx, &newReq)
	if err != nil {
		/*
			special handling if user email already registered
			meaning user phone number already registered
			as we map user phone number to email with cs+ phonenumber @assetfindr.com
		*/
		if err == errorhandler.ErrUserAlreadyRegistered {
			httpStatus, message := errorhandler.ParseToHttpError(errorhandler.ErrUserPhoneNumberAlreadyRegistered)
			c.JSON(httpStatus, gin.H{"error": message})
			return
		}
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, response)
}
