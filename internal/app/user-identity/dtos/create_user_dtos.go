package dtos

import (
	"assetfindr/pkg/common/helpers"

	"github.com/go-playground/validator/v10"
	"gopkg.in/guregu/null.v4"
)

type CreateUserReq struct {
	FirstName         string           `json:"first_name" validate:"required"`
	LastName          string           `json:"last_name"`
	ReferenceID       string           `json:"reference_id"`
	Email             string           `json:"email" validate:"required,email"`
	Password          string           `json:"password"`
	PhoneNumber       string           `json:"phone_number"`
	ReferenceRole     string           `json:"reference_role"`
	PermissionGroupID string           `json:"permission_group_id" validate:"required"`
	Photo             string           `json:"photo"`
	DepartmentID      string           `json:"department_id"`
	NewDepartment     NewDepartmentReq `json:"new_department"`
	Remark            string           `json:"remark"`
}

type NewDepartmentReq struct {
	Name           string `json:"name"`
	DepartmentCode string `json:"department_code"`
	ContactUserID  string `json:"contact_user_id"`
	StatusCode     string `json:"status_code"`
}

var validate *validator.Validate

func (r *CreateUserReq) Vaildate() error {
	return helpers.GetValidate().Struct(r)
}

type UpdateUserReq struct {
	FirstName         string      `json:"first_name"`
	LastName          string      `json:"last_name"`
	ReferenceID       string      `json:"reference_id"`
	PhoneNumber       string      `json:"phone_number"`
	ReferenceRole     string      `json:"reference_role"`
	PermissionGroupID string      `json:"permission_group_id"`
	Photo             string      `json:"photo"`
	DepartmentID      null.String `json:"department_id"`
}

type AddUsersToSubClientReq struct {
	UserIDs        []string `json:"user_ids"`
	ParentClientID string   `json:"parent_client_id"`
	ChildClientID  string   `json:"child_client_id"`
}

type RegisterAFChallange struct {
	FirstName         string           `json:"first_name" validate:"required"`
	LastName          string           `json:"last_name"`
	ReferenceID       string           `json:"reference_id"`
	Email             string           `json:"email" validate:"required,email"`
	Password          string           `json:"password"`
	PhoneNumber       string           `json:"phone_number"`
	ReferenceRole     string           `json:"reference_role"`
	CompanyName       string           `json:"company_name"`
	RelatedIndustry   string           `json:"related_industry"`
	PermissionGroupID string           `json:"permission_group_id" validate:"required"`
	Photo             string           `json:"photo"`
	DepartmentID      string           `json:"department_id"`
	NewDepartment     NewDepartmentReq `json:"new_department"`
}
