package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type HaulHandler struct {
	HaulUseCase *usecase.HaulUseCase
}

func NewHaulHandler(
	haulUseCase *usecase.HaulUseCase,
) *HaulHandler {
	return &HaulHandler{
		HaulUseCase: haulUseCase,
	}
}

func (h *<PERSON><PERSON><PERSON>andler) DriverCheckIn(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	var req dtos.DriverCheckIn
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.DriverCheckIn(ctx, imei, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.J<PERSON>(http.StatusOK, resp)
}

func (h *HaulHandler) DriverCheckOut(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	resp, err := h.HaulUseCase.DriverCheckOut(ctx, imei)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
