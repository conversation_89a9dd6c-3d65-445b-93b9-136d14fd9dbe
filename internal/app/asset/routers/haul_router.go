package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterHaulRoutes(route *gin.Engine, haulHandler *handler.HaulHandler) *gin.Engine {

	haulPublicRoutes := route.Group("/v1/hauls/devices/:imei")
	{
		haulPublicRoutes.POST("/driver-sessions/check-in", haulHandler.DriverCheckIn)
	}

	haulPrivateRoutes := route.Group("/v1/hauls/devices/:imei", middleware.TokenValidationMiddleware())
	{
		haulPrivateRoutes.POST("/driver-sessions/check-out", haulHandler.DriverCheckOut)
	}

	return route
}
