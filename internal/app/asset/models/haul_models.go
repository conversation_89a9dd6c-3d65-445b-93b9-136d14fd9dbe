package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Hauling struct {
	commonmodel.ModelV2
	AssetID           string    `json:"asset_id" gorm:"type:varchar(40);not null"`
	OperatorUserID    string    `json:"operator_user_id" gorm:"type:varchar(40);not null"`
	HaulStatusCode    string    `json:"haul_status_code" gorm:"type:varchar(20);not null"`
	HaulSubStatusCode string    `json:"haul_sub_status_code" gorm:"type:varchar(50);not null"`
	StartTime         time.Time `json:"start_time" gorm:"not null"`
	EndTime           null.Time `json:"end_time" gorm:"default:null"`

	// Relationships
	Asset         Asset        `json:"asset" gorm:"foreignKey:AssetID;references:ID"`
	HaulStatus    HaulStatus   `json:"haul_status" gorm:"foreignKey:HaulStatusCode;references:Code"`
	HaulSubStatus HaulActivity `json:"haul_sub_status" gorm:"foreignKey:HaulSubStatusCode;references:Code"`
}

func (h *Hauling) TableName() string {
	return "ams_haulings"
}

func (h *Hauling) BeforeCreate(db *gorm.DB) error {
	h.SetUUID("hul")
	h.ModelV2.BeforeCreate(db)
	return nil
}

func (h *Hauling) BeforeUpdate(db *gorm.DB) error {
	h.ModelV2.BeforeUpdate(db)
	return nil
}

type HaulingWhere struct {
	ID                string
	AssetID           string
	OperatorUserID    string
	HaulStatusCode    string
	HaulSubStatusCode string
	ClientID          string
	IsActive          *bool // For filtering ongoing haulings (EndTime is null)
}

type HaulingPreload struct {
	HaulStatus    bool
	HaulSubStatus bool
}

type HaulingCondition struct {
	Where   HaulingWhere
	Columns []string
	Preload HaulingPreload
}
