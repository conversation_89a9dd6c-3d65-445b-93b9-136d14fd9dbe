package models

import (
	"gopkg.in/guregu/null.v4"
)

type HaulStatus struct {
	Code        string `json:"code" gorm:"primaryKey;type:varchar(20)"`
	Label       string `json:"label" gorm:"type:varchar(20);not null"`
	Description string `json:"description" gorm:"type:varchar(50);not null"`

	// Relationships
	HaulSubStatuses []HaulActivity `json:"haul_sub_statuses" gorm:"foreignKey:MainStatusCode;references:Code"`
}

func (hs *HaulStatus) TableName() string {
	return "ams_HAUL_STATUSES"
}

type HaulActivity struct {
	Code           string    `json:"code" gorm:"primaryKey;type:varchar(50)"`
	Label          string    `json:"label" gorm:"type:varchar(100);not null"`
	Description    string    `json:"description" gorm:"type:varchar(100);not null"`
	MainStatusCode string    `json:"main_status_code" gorm:"type:varchar(20);not null"`
	IsLoad         null.Bool `json:"is_load" gorm:"default:null"`

	// Relationships
	MainStatus HaulStatus `json:"main_status" gorm:"foreignKey:MainStatusCode;references:Code"`
}

func (hss *HaulActivity) TableName() string {
	return "ams_HAUL_SUB_STATUSES"
}

type HaulSubStatusCycle struct {
	PrevCode string `json:"prev_code" gorm:"primaryKey;type:varchar(50)"`
	NextCode string `json:"next_code" gorm:"primaryKey;type:varchar(50)"`

	// Relationships
	PreviousStatus HaulActivity `json:"previous_status" gorm:"foreignKey:PrevCode;references:Code"`
	NextStatus     HaulActivity `json:"next_status" gorm:"foreignKey:NextCode;references:Code"`
}

func (hssc *HaulSubStatusCycle) TableName() string {
	return "ams_HAUL_SUB_STATUS_CYCLES"
}

type HaulStatusWhere struct {
	Code  string
	Codes []string
}

type HaulSubStatusWhere struct {
	Code           string
	Codes          []string
	MainStatusCode string
	IsLoad         *bool
}

type HaulStatusPreload struct {
	HaulSubStatuses bool
}

type HaulStatusCondition struct {
	Where   HaulStatusWhere
	Columns []string
	Preload HaulStatusPreload
}
