package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type DriverLoginSession struct {
	commonmodel.ModelV2
	AssetID       string    `json:"asset_id" gorm:"type:varchar(40);not null"`
	UserID        string    `json:"user_id" gorm:"type:varchar(40);not null"`
	LoggedInTime  time.Time `json:"logged_in_time" gorm:"not null"`
	LoggedOutTime null.Time `json:"logged_out_time" gorm:"default:null"`

	// Relationships
	Asset Asset `json:"asset" gorm:"foreignKey:AssetID;references:ID"`
}

func (dls *DriverLoginSession) TableName() string {
	return "ams_driver_login_sessions"
}

func (dls *DriverLoginSession) BeforeCreate(db *gorm.DB) error {
	dls.SetUUID("dls")
	dls.ModelV2.BeforeCreate(db)
	return nil
}

func (dls *DriverLoginSession) BeforeUpdate(db *gorm.DB) error {
	dls.ModelV2.BeforeUpdate(db)
	return nil
}

type DriverLoginSessionWhere struct {
	ID       string
	AssetID  string
	UserID   string
	ClientID string
	IsActive null.Bool // For filtering active sessions (LoggedOutTime is null)
}

type DriverLoginSessionCondition struct {
	Where   DriverLoginSessionWhere
	Columns []string
}
