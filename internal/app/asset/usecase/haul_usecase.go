package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	integrationConstant "assetfindr/internal/app/integration/constants"
	integrationModels "assetfindr/internal/app/integration/models"
	integrationRepo "assetfindr/internal/app/integration/repository"
	userDtos "assetfindr/internal/app/user-identity/dtos"
	userModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepo "assetfindr/internal/app/user-identity/repository"
	userIdentityUsecase "assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"time"

	"gopkg.in/guregu/null.v4"
)

type HaulUseCase struct {
	DB              database.DBUsecase
	haulRepo        repository.HaulRepository
	integrationRepo integrationRepo.IntegrationRepository
	userRepo        userIdentityRepo.UserRepository
	userUsecase     *userIdentityUsecase.UserUseCase
}

func NewHaulUseCase(
	DB database.DBUsecase,
	haulRepo repository.HaulRepository,
	integrationRepo integrationRepo.IntegrationRepository,
	userRepo userIdentityRepo.UserRepository,
	userUsecase *userIdentityUsecase.UserUseCase,
) *HaulUseCase {
	return &HaulUseCase{
		DB:              DB,
		haulRepo:        haulRepo,
		integrationRepo: integrationRepo,
		userRepo:        userRepo,
		userUsecase:     userUsecase,
	}
}

func (uc *HaulUseCase) DriverCheckIn(ctx context.Context, imei string, req dtos.DriverCheckIn) (*userDtos.UserAuthResponse, error) {
	userCLient, err := uc.userRepo.GetUserClient(ctx, uc.DB.DB(), userModels.UserClientCondition{
		Where: userModels.UserClientWhere{
			UnencryptedPIN: req.PIN,
		},
		Preload: userModels.UserClientPreload{
			User: true,
		},
	})
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return nil, errorhandler.ErrBadRequest("Invalid PIN")
		}

		return nil, err
	}

	if userCLient.User == nil {
		return nil, errorhandler.ErrBadRequest("User not found")
	}

	if !userCLient.User.IsActive() {
		return nil, errorhandler.ErrNotAllowed("trying to login in inactive user, please contact your administrator")
	}

	clientID := userCLient.ClientID

	integrationDevice, err := uc.integrationRepo.GetIntegrationDevice(ctx, uc.DB.DB(), integrationModels.IntegrationDeviceCondition{
		Where: integrationModels.IntegrationDeviceWhere{
			ReferenceCode:       imei,
			IntegrationTypeCode: integrationConstant.INTEGRATION_TYPE_CODE_DEVICE_TABLET,
			ClientID:            clientID,
		},
		Preload: integrationModels.IntegrationDevicePreload{
			Integration: true,
		},
	})
	if err != nil {
		return nil, err
	}

	currDriverLoginSession, err := uc.haulRepo.GetDriverLoginSession(ctx, uc.DB.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if currDriverLoginSession != nil {
		if currDriverLoginSession.UserID != userCLient.UserID {
			return nil, errorhandler.ErrBadRequest("Asset is already logged in by another user")
		}
	} else {
		err = uc.haulRepo.CreateDriverLoginSession(ctx, uc.DB.DB(), &models.DriverLoginSession{
			ModelV2: commonmodel.ModelV2{
				UpdatedBy: userCLient.UserID,
				CreatedBy: userCLient.UserID,
				ClientID:  clientID,
			},
			AssetID:      integrationDevice.Integration.InternalReferenceID,
			UserID:       userCLient.UserID,
			LoggedInTime: time.Now(),
		})
		if err != nil {
			return nil, err
		}
	}

	// TO DO: Handle Haulings

	return uc.userUsecase.UserLoginByUserClient(ctx, userCLient)
}

func (uc *HaulUseCase) DriverCheckOut(ctx context.Context, imei string) (*commonmodel.UpdateResponse, error) {
	currDriverLoginSession, err := uc.ValidateCurrentLogin(ctx, imei)
	if err != nil {
		return nil, err
	}

	// TODO: Handle Haulings

	err = uc.haulRepo.UpdateDriverLoginSession(ctx, uc.DB.DB(), currDriverLoginSession.ID, &models.DriverLoginSession{
		LoggedOutTime: null.TimeFrom(time.Now()),
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *HaulUseCase) ValidateCurrentLogin(ctx context.Context, imei string) (*models.DriverLoginSession, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationDevice, err := uc.integrationRepo.GetIntegrationDevice(ctx, uc.DB.DB(), integrationModels.IntegrationDeviceCondition{
		Where: integrationModels.IntegrationDeviceWhere{
			ReferenceCode:       imei,
			IntegrationTypeCode: integrationConstant.INTEGRATION_TYPE_CODE_DEVICE_TABLET,
		},
		Preload: integrationModels.IntegrationDevicePreload{
			Integration: true,
		},
	})
	if err != nil {
		return nil, err
	}

	currDriverLoginSession, err := uc.haulRepo.GetDriverLoginSession(ctx, uc.DB.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if currDriverLoginSession == nil {
		return nil, errorhandler.ErrBadRequest("Asset is not logged in")
	}

	if currDriverLoginSession.UserID != claim.UserID {
		return nil, errorhandler.ErrBadRequest("Asset is logged in by another user")
	}

	return currDriverLoginSession, nil
}
