package constants

// Haul Status Codes
const (
	HAUL_STATUS_CODE_P2H       = "P2H"
	HAUL_STATUS_CODE_READY     = "READY"
	HAUL_STATUS_CODE_DELAY     = "DELAY"
	HAUL_STATUS_CODE_IDLE      = "IDLE"
	HAUL_STATUS_CODE_BREAKDOWN = "BREAKDOWN"
)

// Haul Status Labels
const (
	HAUL_STATUS_LABEL_P2H       = "P2H"
	HAUL_STATUS_LABEL_READY     = "Ready"
	HAUL_STATUS_LABEL_DELAY     = "Delay"
	HAUL_STATUS_LABEL_IDLE      = "Idle"
	HAUL_STATUS_LABEL_BREAKDOWN = "Breakdown"
)

// Haul Sub Status Codes
const (
	HAUL_SUB_STATUS_CODE_P2H                      = "P2H"
	HAUL_SUB_STATUS_CODE_TRAVELING_TO_LOADING     = "TRAVELING_TO_LOADING"
	HAUL_SUB_STATUS_CODE_QUEUEING_FOR_LOADING     = "QUEUEING_FOR_LOADING"
	HAUL_SUB_STATUS_CODE_LOADING                  = "LOADING"
	HAUL_SUB_STATUS_CODE_TRAVELING_TO_WB_LOAD     = "TRAVELING_TO_WB_LOAD"
	HAUL_SUB_STATUS_CODE_QUEUEING_FOR_WB_LOAD     = "QUEUEING_FOR_WB_LOAD"
	HAUL_SUB_STATUS_CODE_WEIGHING_THE_LOAD        = "WEIGHING_THE_LOAD"
	HAUL_SUB_STATUS_CODE_TRAVELING_TO_DUMPING     = "TRAVELING_TO_DUMPING"
	HAUL_SUB_STATUS_CODE_QUEUEING_FOR_DUMPING     = "QUEUEING_FOR_DUMPING"
	HAUL_SUB_STATUS_CODE_DUMPING                  = "DUMPING"
	HAUL_SUB_STATUS_CODE_TRAVELING_TO_WB_EMPTY    = "TRAVELING_TO_WB_EMPTY"
	HAUL_SUB_STATUS_CODE_QUEUEING_FOR_WB_EMPTY    = "QUEUEING_FOR_WB_EMPTY"
	HAUL_SUB_STATUS_CODE_WEIGHING_EMPTY           = "WEIGHING_EMPTY"
	HAUL_SUB_STATUS_CODE_SHIFT_CHANGE             = "SHIFT_CHANGE"
	HAUL_SUB_STATUS_CODE_REST_MEALS               = "REST_MEALS"
	HAUL_SUB_STATUS_CODE_UNIT_CLEANING            = "UNIT_CLEANING"
	HAUL_SUB_STATUS_CODE_WORSHIP_SERVICE          = "WORSHIP_SERVICE"
	HAUL_SUB_STATUS_CODE_WEIGHBRIDGE_PROBLEMS     = "WEIGHBRIDGE_PROBLEMS"
	HAUL_SUB_STATUS_CODE_FATIGUE_TEST             = "FATIGUE_TEST"
	HAUL_SUB_STATUS_CODE_ROAD_REPAIRS             = "ROAD_REPAIRS"
	HAUL_SUB_STATUS_CODE_WEATHER_CONDITIONS       = "WEATHER_CONDITIONS"
	HAUL_SUB_STATUS_CODE_CUSTOMER_ORDER           = "CUSTOMER_ORDER"
	HAUL_SUB_STATUS_CODE_PERIODIC_INSPECTION      = "PERIODIC_INSPECTION"
	HAUL_SUB_STATUS_CODE_SCHEDULE_MAINTENANCE     = "SCHEDULE_MAINTENANCE"
	HAUL_SUB_STATUS_CODE_TIRE_MAINTENANCE         = "TIRE_MAINTENANCE"
	HAUL_SUB_STATUS_CODE_UNSCHEDULE_MAINTENANCE   = "UNSCHEDULE_MAINTENANCE"
)

// Haul Sub Status Labels
const (
	HAUL_SUB_STATUS_LABEL_P2H                      = "P2H"
	HAUL_SUB_STATUS_LABEL_TRAVELING_TO_LOADING     = "Traveling to Loading"
	HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_LOADING     = "Antri Loading"
	HAUL_SUB_STATUS_LABEL_LOADING                  = "Loading"
	HAUL_SUB_STATUS_LABEL_TRAVELING_TO_WB_LOAD     = "Traveling to WB (Muatan)"
	HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_WB_LOAD     = "Antri WB (Muatan)"
	HAUL_SUB_STATUS_LABEL_WEIGHING_THE_LOAD        = "Timbang Muatan"
	HAUL_SUB_STATUS_LABEL_TRAVELING_TO_DUMPING     = "Traveling to Dumping"
	HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_DUMPING     = "Antri Dumping"
	HAUL_SUB_STATUS_LABEL_DUMPING                  = "Dumping"
	HAUL_SUB_STATUS_LABEL_TRAVELING_TO_WB_EMPTY    = "Traveling to WB (Kosong)"
	HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_WB_EMPTY    = "Antri WB (Kosong)"
	HAUL_SUB_STATUS_LABEL_WEIGHING_EMPTY           = "Timbang Kosong"
	HAUL_SUB_STATUS_LABEL_SHIFT_CHANGE             = "Ganti Shift"
	HAUL_SUB_STATUS_LABEL_REST_MEALS               = "Istirahat & Makan"
	HAUL_SUB_STATUS_LABEL_UNIT_CLEANING            = "Pembersihan Unit"
	HAUL_SUB_STATUS_LABEL_WORSHIP_SERVICE          = "Ibadah"
	HAUL_SUB_STATUS_LABEL_WEIGHBRIDGE_PROBLEMS     = "Weightbridge Bermasalah"
	HAUL_SUB_STATUS_LABEL_FATIGUE_TEST             = "Fatigue Test"
	HAUL_SUB_STATUS_LABEL_ROAD_REPAIRS             = "Perbaikan Jalan"
	HAUL_SUB_STATUS_LABEL_WEATHER_CONDITIONS       = "Kondisi Cuaca"
	HAUL_SUB_STATUS_LABEL_CUSTOMER_ORDER           = "Customer Order"
	HAUL_SUB_STATUS_LABEL_PERIODIC_INSPECTION      = "Periodic Inspection"
	HAUL_SUB_STATUS_LABEL_SCHEDULE_MAINTENANCE     = "Schedule Maintenance"
	HAUL_SUB_STATUS_LABEL_TIRE_MAINTENANCE         = "Tyre Maintenance"
	HAUL_SUB_STATUS_LABEL_UNSCHEDULE_MAINTENANCE   = "Unschedule Maintenance"
)

// Map functions for status code to label mapping
func MapHaulStatusCodeToLabel() map[string]string {
	return map[string]string{
		HAUL_STATUS_CODE_P2H:       HAUL_STATUS_LABEL_P2H,
		HAUL_STATUS_CODE_READY:     HAUL_STATUS_LABEL_READY,
		HAUL_STATUS_CODE_DELAY:     HAUL_STATUS_LABEL_DELAY,
		HAUL_STATUS_CODE_IDLE:      HAUL_STATUS_LABEL_IDLE,
		HAUL_STATUS_CODE_BREAKDOWN: HAUL_STATUS_LABEL_BREAKDOWN,
	}
}

func MapHaulSubStatusCodeToLabel() map[string]string {
	return map[string]string{
		HAUL_SUB_STATUS_CODE_P2H:                      HAUL_SUB_STATUS_LABEL_P2H,
		HAUL_SUB_STATUS_CODE_TRAVELING_TO_LOADING:     HAUL_SUB_STATUS_LABEL_TRAVELING_TO_LOADING,
		HAUL_SUB_STATUS_CODE_QUEUEING_FOR_LOADING:     HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_LOADING,
		HAUL_SUB_STATUS_CODE_LOADING:                  HAUL_SUB_STATUS_LABEL_LOADING,
		HAUL_SUB_STATUS_CODE_TRAVELING_TO_WB_LOAD:     HAUL_SUB_STATUS_LABEL_TRAVELING_TO_WB_LOAD,
		HAUL_SUB_STATUS_CODE_QUEUEING_FOR_WB_LOAD:     HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_WB_LOAD,
		HAUL_SUB_STATUS_CODE_WEIGHING_THE_LOAD:        HAUL_SUB_STATUS_LABEL_WEIGHING_THE_LOAD,
		HAUL_SUB_STATUS_CODE_TRAVELING_TO_DUMPING:     HAUL_SUB_STATUS_LABEL_TRAVELING_TO_DUMPING,
		HAUL_SUB_STATUS_CODE_QUEUEING_FOR_DUMPING:     HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_DUMPING,
		HAUL_SUB_STATUS_CODE_DUMPING:                  HAUL_SUB_STATUS_LABEL_DUMPING,
		HAUL_SUB_STATUS_CODE_TRAVELING_TO_WB_EMPTY:    HAUL_SUB_STATUS_LABEL_TRAVELING_TO_WB_EMPTY,
		HAUL_SUB_STATUS_CODE_QUEUEING_FOR_WB_EMPTY:    HAUL_SUB_STATUS_LABEL_QUEUEING_FOR_WB_EMPTY,
		HAUL_SUB_STATUS_CODE_WEIGHING_EMPTY:           HAUL_SUB_STATUS_LABEL_WEIGHING_EMPTY,
		HAUL_SUB_STATUS_CODE_SHIFT_CHANGE:             HAUL_SUB_STATUS_LABEL_SHIFT_CHANGE,
		HAUL_SUB_STATUS_CODE_REST_MEALS:               HAUL_SUB_STATUS_LABEL_REST_MEALS,
		HAUL_SUB_STATUS_CODE_UNIT_CLEANING:            HAUL_SUB_STATUS_LABEL_UNIT_CLEANING,
		HAUL_SUB_STATUS_CODE_WORSHIP_SERVICE:          HAUL_SUB_STATUS_LABEL_WORSHIP_SERVICE,
		HAUL_SUB_STATUS_CODE_WEIGHBRIDGE_PROBLEMS:     HAUL_SUB_STATUS_LABEL_WEIGHBRIDGE_PROBLEMS,
		HAUL_SUB_STATUS_CODE_FATIGUE_TEST:             HAUL_SUB_STATUS_LABEL_FATIGUE_TEST,
		HAUL_SUB_STATUS_CODE_ROAD_REPAIRS:             HAUL_SUB_STATUS_LABEL_ROAD_REPAIRS,
		HAUL_SUB_STATUS_CODE_WEATHER_CONDITIONS:       HAUL_SUB_STATUS_LABEL_WEATHER_CONDITIONS,
		HAUL_SUB_STATUS_CODE_CUSTOMER_ORDER:           HAUL_SUB_STATUS_LABEL_CUSTOMER_ORDER,
		HAUL_SUB_STATUS_CODE_PERIODIC_INSPECTION:      HAUL_SUB_STATUS_LABEL_PERIODIC_INSPECTION,
		HAUL_SUB_STATUS_CODE_SCHEDULE_MAINTENANCE:     HAUL_SUB_STATUS_LABEL_SCHEDULE_MAINTENANCE,
		HAUL_SUB_STATUS_CODE_TIRE_MAINTENANCE:         HAUL_SUB_STATUS_LABEL_TIRE_MAINTENANCE,
		HAUL_SUB_STATUS_CODE_UNSCHEDULE_MAINTENANCE:   HAUL_SUB_STATUS_LABEL_UNSCHEDULE_MAINTENANCE,
	}
}

// Helper functions to check status types
func IsHaulSubStatusLoad(code string) bool {
	loadStatuses := []string{
		HAUL_SUB_STATUS_CODE_LOADING,
		HAUL_SUB_STATUS_CODE_WEIGHING_THE_LOAD,
		HAUL_SUB_STATUS_CODE_TRAVELING_TO_DUMPING,
		HAUL_SUB_STATUS_CODE_QUEUEING_FOR_DUMPING,
		HAUL_SUB_STATUS_CODE_DUMPING,
		HAUL_SUB_STATUS_CODE_WEIGHING_EMPTY,
	}
	
	for _, status := range loadStatuses {
		if status == code {
			return true
		}
	}
	return false
}

func GetHaulSubStatusesByMainStatus(mainStatusCode string) []string {
	switch mainStatusCode {
	case HAUL_STATUS_CODE_P2H:
		return []string{HAUL_SUB_STATUS_CODE_P2H}
	case HAUL_STATUS_CODE_READY:
		return []string{
			HAUL_SUB_STATUS_CODE_TRAVELING_TO_LOADING,
			HAUL_SUB_STATUS_CODE_QUEUEING_FOR_LOADING,
			HAUL_SUB_STATUS_CODE_LOADING,
			HAUL_SUB_STATUS_CODE_TRAVELING_TO_WB_LOAD,
			HAUL_SUB_STATUS_CODE_QUEUEING_FOR_WB_LOAD,
			HAUL_SUB_STATUS_CODE_WEIGHING_THE_LOAD,
			HAUL_SUB_STATUS_CODE_TRAVELING_TO_DUMPING,
			HAUL_SUB_STATUS_CODE_QUEUEING_FOR_DUMPING,
			HAUL_SUB_STATUS_CODE_DUMPING,
			HAUL_SUB_STATUS_CODE_TRAVELING_TO_WB_EMPTY,
			HAUL_SUB_STATUS_CODE_QUEUEING_FOR_WB_EMPTY,
			HAUL_SUB_STATUS_CODE_WEIGHING_EMPTY,
		}
	case HAUL_STATUS_CODE_DELAY:
		return []string{
			HAUL_SUB_STATUS_CODE_SHIFT_CHANGE,
			HAUL_SUB_STATUS_CODE_REST_MEALS,
			HAUL_SUB_STATUS_CODE_UNIT_CLEANING,
			HAUL_SUB_STATUS_CODE_WORSHIP_SERVICE,
			HAUL_SUB_STATUS_CODE_WEIGHBRIDGE_PROBLEMS,
			HAUL_SUB_STATUS_CODE_FATIGUE_TEST,
			HAUL_SUB_STATUS_CODE_ROAD_REPAIRS,
		}
	case HAUL_STATUS_CODE_IDLE:
		return []string{
			HAUL_SUB_STATUS_CODE_WEATHER_CONDITIONS,
			HAUL_SUB_STATUS_CODE_CUSTOMER_ORDER,
		}
	case HAUL_STATUS_CODE_BREAKDOWN:
		return []string{
			HAUL_SUB_STATUS_CODE_PERIODIC_INSPECTION,
			HAUL_SUB_STATUS_CODE_SCHEDULE_MAINTENANCE,
			HAUL_SUB_STATUS_CODE_TIRE_MAINTENANCE,
			HAUL_SUB_STATUS_CODE_UNSCHEDULE_MAINTENANCE,
		}
	default:
		return []string{}
	}
}
