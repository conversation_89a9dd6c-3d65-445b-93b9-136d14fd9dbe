package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type HaulRepository interface {
	GetDriverLoginSession(ctx context.Context, dB database.DBI, cond models.DriverLoginSessionCondition) (*models.DriverLoginSession, error)
	CreateDriverLoginSession(ctx context.Context, dB database.DBI, driverLoginSession *models.DriverLoginSession) error
	UpdateDriverLoginSession(ctx context.Context, dB database.DBI, id string, driverLoginSession *models.DriverLoginSession) error
}
