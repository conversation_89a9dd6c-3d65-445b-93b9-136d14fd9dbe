package repository

import (
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/infrastructure/database"

	"context"
)

type IntegrationRepository interface {
	CreateIntegrationAccount(ctx context.Context, dB database.DBI, integrationAccount *models.IntegrationAccount) error
	GetIntegrationAccount(ctx context.Context, dB database.DBI, cond models.IntegrationAccountCondition) (*models.IntegrationAccount, error)
	GetIntegrationAccountList(ctx context.Context, dB database.DBI, param models.GetIntegrationAccountListParam) (int, []models.IntegrationAccount, error)
	GetIntegrationAccountTargetList(ctx context.Context, dB database.DBI, param models.GetIntegrationTargetListParam) (int, []models.IntegrationTarget, error)
	GetIntegrationAccountTargetTypeList(ctx context.Context, dB database.DBI, param models.GetIntegrationTargetTypeListParam) (int, []models.IntegrationTargetType, error)
	GetIntegrationAccountTargetType(ctx context.Context, dB database.DBI, cond models.IntegrationTargetTypeCondition) (*models.IntegrationTargetType, error)
	UpdateIntegrationAccount(ctx context.Context, dB database.DBI, id string, integrationAccount *models.IntegrationAccount) error
	CreateIntegration(ctx context.Context, dB database.DBI, integration *models.Integration) error
	UpsertIntegration(ctx context.Context, dB database.DBI, integration *models.Integration) error
	GetIntegration(ctx context.Context, dB database.DBI, cond models.IntegrationCondition) (*models.Integration, error)
	GetIntegrations(ctx context.Context, dB database.DBI, cond models.IntegrationCondition) ([]models.Integration, error)
	GetIntegrationList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []models.Integration, error)
	GetIntegrationDistinctRefIDList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []string, error)
	GetIntegrationTrackingList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []models.Integration, error)
	UpdateIntegration(ctx context.Context, dB database.DBI, id string, integration *models.Integration) error
	DeleteIntegration(ctx context.Context, dB database.DBI, id string) error
	GetIntegrationVehicleList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []models.Integration, error)

	UpsertIntegrationSession(ctx context.Context, dB database.DBI, integrationSession *models.IntegrationSession) error
	GetIntegrationSession(ctx context.Context, dB database.DBI, cond models.IntegrationSessionCondition) (*models.IntegrationSession, error)

	GetIntegrationTranslogicCalibrationList(ctx context.Context, dB database.DBI, param models.GetIntegrationTranslogicCalibrationListParam) (int, []models.IntegrationTranslogicCalibration, error)
	CreateTranslogicCalibration(ctx context.Context, dB database.DBI, translogicCalibration *models.IntegrationTranslogicCalibration) error

	UpsertMonitoringDisplayConfig(ctx context.Context, dB database.DBI, monitoringDisCon *models.MonitoringDisplayConfig) error
	GetMonitoringDisplayConfig(ctx context.Context, dB database.DBI, clientID string) (*models.MonitoringDisplayConfig, error)

	CreateIntegrationDataMappingTemplate(ctx context.Context, dB database.DBI, integrationDataMappingTemplate *models.IntegrationDataMappingTemplate) error
	GetIntegrationDataMappingTemplate(ctx context.Context, dB database.DBI, cond models.IntegrationDataMappingTemplateCondition) (*models.IntegrationDataMappingTemplate, error)
	GetIntegrationDataMappingTemplateList(ctx context.Context, dB database.DBI, param models.GetIntegrationDataMappingTemplateListParam) (int, []models.IntegrationDataMappingTemplate, error)
	UpdateIntegrationDataMappingTemplate(ctx context.Context, dB database.DBI, id string, integrationDataMappingTemplate *models.IntegrationDataMappingTemplate) error
	DeActivateIntegrationByRefID(ctx context.Context, dB database.DBI, internalRefID string) error

	GetIntegrationCommandsList(ctx context.Context, dB database.DBI, param models.IntegrationCommandsListParam) (int, []models.IntegrationCommand, error)
	CreateIntegrationCommand(ctx context.Context, dB database.DBI, integrationCommand *models.IntegrationCommand) error

	GetManualCanBusMappings(ctx context.Context, dB database.DBI, cond models.ManualCanBusMappingCondition) ([]models.ManualCanBusMapping, error)

	GetIntegrationDeviceList(ctx context.Context, dB database.DBI, param models.GetIntegrationDeviceListParam) (int, []models.IntegrationDevice, error)
	GetIntegrationDeviceDetail(ctx context.Context, dB database.DBI, id string) (models.IntegrationDevice, error)
	GetIntegrationDevice(ctx context.Context, dB database.DBI, cond models.IntegrationDeviceCondition) (*models.IntegrationDevice, error)
	GetIntegrationWithActiveIntegrationDevice(ctx context.Context, dB database.DBI, assetTyreID string) (*models.Integration, error)
	CreateIntegrationDevice(ctx context.Context, dB database.DBI, integrationDevice *models.IntegrationDevice) error
	UpdateIntegrationDevice(ctx context.Context, dB database.DBI, id string, integrationDevice *models.IntegrationDevice) error

	GetIntegrationDeviceHistoryList(ctx context.Context, dB database.DBI, param models.GetIntegrationDeviceHistoryListParam) (int, []models.IntegrationDeviceHistory, error)
	GetLatestIntegrationDeviceHistory(ctx context.Context, dB database.DBI, cond models.IntegrationDeviceHistoryCondition) (*models.IntegrationDeviceHistory, error)
	CreateIntegrationDeviceHistory(ctx context.Context, dB database.DBI, integrationDeviceHistory *models.IntegrationDeviceHistory) error
	UpdateIntegrationDeviceHistory(ctx context.Context, dB database.DBI, id string, integrationDeviceHistory *models.IntegrationDeviceHistory) error
}
