package errorhandler

import "net/http"

func ParseToHttpError(err error) (httpStatus int, errMessage interface{}) {
	if err == ErrUserAlreadyRegistered {
		return http.StatusBadRequest, err.Error()
	}

	if err == ErrUserPhoneNumberAlreadyRegistered {
		return http.StatusBadRequest, err.Error()
	}

	if err == ErrUnauthorized {
		return http.StatusUnauthorized, err.Error()
	}

	if err == ErrNotEligible {
		return http.StatusBadGateway, err.Error()
	}

	if val, ok := err.(ErrExternal); ok {
		return http.StatusBadGateway, val
	}

	if val, ok := err.(errAlreadyExist); ok {
		return http.StatusBadRequest, val.Error()
	}

	if val, ok := err.(errNotFound); ok {
		return http.StatusBadRequest, val.Error()
	}

	if val, ok := err.(errNotAllowed); ok {
		return http.StatusNotAcceptable, val.Error()
	}

	if val, ok := err.(errBadRequest); ok {
		return http.StatusBadRequest, val.Error()
	}

	if val, ok := err.(errInternalServerError); ok {
		return http.StatusInternalServerError, val.Error()
	}

	return http.StatusInternalServerError, err.Error()
}
