
BEGIN;

CREATE TABLE IF NOT EXISTS "ams_HAUL_STATUSES" (
    "code" VARCHAR(20) NOT NULL,
    "label" VARCHAR(20) NOT NULL,
    "description" VARCHAR(50) NOT NULL,
    PRIMARY KEY ("code")
);

CREATE TABLE IF NOT EXISTS "ams_HAUL_ACTIVITIES" (
    "code" VARCHAR(50) NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "description" VARCHAR(100) NOT NULL,
    "haul_status_code" VARCHAR(20) NOT NULL,
    "rank" SMALLINT,
    "is_load" BOOLEAN,
    PRIMARY KEY ("code"),
    FOREIGN KEY ("haul_status_code") REFERENCES "ams_HAUL_STATUSES"("code")
);

CREATE TABLE IF NOT EXISTS "ams_HAUL_SUB_ACTIVITIES" (
    "code" VARCHAR(50) NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "description" VARCHAR(100) NOT NULL,
    "main_activity_code" VARCHAR(20) NOT NULL,
    PRIMARY KEY ("code"),
    FOREIGN KEY ("main_activity_code") REFERENCES "ams_HAUL_ACTIVITIES"("code")
);

CREATE TABLE IF NOT EXISTS "ams_HAUL_ACTIVITY_CYCLES" (
    prev_code VARCHAR(50) NOT NULL,
    next_code VARCHAR(50) NOT NULL,
    PRIMARY KEY (prev_code, next_code),
    FOREIGN KEY (prev_code) REFERENCES "ams_HAUL_SUB_STATUSES"("code"),
    FOREIGN KEY (next_code) REFERENCES "ams_HAUL_SUB_STATUSES"("code")
);


INSERT INTO "ams_HAUL_STATUSES" (code, label, description) VALUES
    ('P2H', 'P2H', 'Pit to Hauling'),
    ('READY', 'Ready', 'Ready for operation'),
    ('DELAY', 'Delay', 'Delayed operation'),
    ('IDLE', 'Idle', 'Idle state'),
    ('BREAKDOWN', 'Breakdown', 'Equipment breakdown')
ON CONFLICT (code) DO NOTHING;

INSERT INTO "ams_HAUL_ACTIVITIES" (code, label, description, is_load, haul_status_code, rank) VALUES
    ('P2H', 'P2H', 'Pit to Hauling', false, 'P2H', null),
    ('TRAVELING_TO_LOADING', 'Traveling to Loading', 'Traveling to Loading', false, 'READY', null),
    ('QUEUEING_FOR_LOADING', 'Antri Loading', 'Queue for Loading', false, 'READY', null),
    ('LOADING', 'Loading', 'Loading Process', true, 'READY', null),
    ('TRAVELING_TO_WB_LOAD', 'Traveling to WB (Muatan)', 'Traveling to Weight Bridge (Loaded)', false, 'READY', null),
    ('QUEUEING_FOR_WB_LOAD', 'Antri WB (Muatan)', 'Queue at Weight Bridge (Loaded)', false, 'READY', null),
    ('WEIGHING_THE_LOAD', 'Timbang Muatan', 'Weighing (Loaded)', true, 'READY', null),
    ('TRAVELING_TO_DUMPING', 'Traveling to Dumping', 'Traveling to Dumping', true, 'READY', null),
    ('QUEUEING_FOR_DUMPING', 'Antri Dumping', 'Queue for Dumping', true, 'READY', null),
    ('DUMPING', 'Dumping', 'Dumping Process', true, 'READY', null),
    ('TRAVELING_TO_WB_EMPTY', 'Traveling to WB (Kosong)', 'Traveling to Weight Bridge (Empty)', false, 'READY', null),
    ('QUEUEING_FOR_WB_EMPTY', 'Antri WB (Kosong)', 'Queue at Weight Bridge (Empty)', false, 'READY', null),
    ('WEIGHING_EMPTY', 'Timbang Kosong', 'Weighing (Empty)', true, 'READY', null),
    ('SHIFT_CHANGE', 'Ganti Shift', 'Shift Change', NULL, 'DELAY'),
    ('REST_MEALS', 'Istirahat & Makan', 'Break & Meal', NULL, 'DELAY'),
    ('UNIT_CLEANING', 'Pembersihan Unit', 'Unit Cleaning', NULL, 'DELAY'),
    ('WORSHIP_SERVICE', 'Ibadah', 'Prayer Time', NULL, 'DELAY'),
    ('WEIGHBRIDGE_PROBLEMS', 'Weightbridge Bermasalah', 'Weight Bridge Problem', NULL, 'DELAY'),
    ('FATIGUE_TEST', 'Fatigue Test', 'Fatigue Test', NULL, 'DELAY'),
    ('ROAD_REPAIRS', 'Perbaikan Jalan', 'Road Repair', NULL, 'DELAY'),
    ('WEATHER_CONDITIONS', 'Kondisi Cuaca', 'Weather Condition', NULL, 'IDLE'),
    ('CUSTOMER_ORDER', 'Customer Order', 'Customer Order', NULL, 'IDLE'),
    ('PERIODIC_INSPECTION', 'Periodic Inspection', 'Periodic Inspection', NULL, 'BREAKDOWN'),
    ('SCHEDULE_MAINTENANCE', 'Schedule Maintenance', 'Schedule Maintenance', NULL, 'BREAKDOWN'),
    ('TIRE_MAINTENANCE', 'Tyre Maintenance', 'Tyre Maintenance', NULL, 'BREAKDOWN'),
    ('UNSCHEDULE_MAINTENANCE', 'Unschedule Maintenance', 'Unschedule Maintenance', NULL, 'BREAKDOWN')
ON CONFLICT (code) DO NOTHING;

INSERT INTO "ams_HAUL_ACTIVITY_CYCLES" (prev_code, next_code) VALUES
    ('TRAVELING_TO_LOADING', 'QUEUEING_FOR_LOADING'),
    ('QUEUEING_FOR_LOADING', 'LOADING'),
    ('LOADING', 'TRAVELING_TO_WB_LOAD'),
    ('TRAVELING_TO_WB_LOAD', 'QUEUEING_FOR_WB_LOAD'),
    ('QUEUEING_FOR_WB_LOAD', 'WEIGHING_THE_LOAD'),
    ('WEIGHING_THE_LOAD', 'TRAVELING_TO_DUMPING'),
    ('TRAVELING_TO_DUMPING', 'QUEUEING_FOR_DUMPING'),
    ('QUEUEING_FOR_DUMPING', 'DUMPING'),
    ('DUMPING', 'TRAVELING_TO_WB_EMPTY'),
    ('TRAVELING_TO_WB_EMPTY', 'QUEUEING_FOR_WB_EMPTY'),
    ('QUEUEING_FOR_WB_EMPTY', 'WEIGHING_EMPTY'),
    ('WEIGHING_EMPTY', 'TRAVELING_TO_LOADING')
ON CONFLICT (prev_code, next_code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ams_haulings" (
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    operator_user_id VARCHAR(40) NOT NULL,
    haul_status_code VARCHAR(20) NOT NULL,
    haul_sub_status_code VARCHAR(50) NOT NULL,  
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,

    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    FOREIGN KEY (haul_status_code) REFERENCES "ams_HAUL_STATUSES"("code"),
    FOREIGN KEY (haul_sub_status_code) REFERENCES "ams_HAUL_SUB_STATUSES"("code")
);

CREATE INDEX IF NOT EXISTS idx_ams_haulings_asset_id ON "ams_haulings" ("asset_id") WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_ams_haulings_client_id ON "ams_haulings" ("client_id") WHERE deleted_at IS NULL;

INSERT INTO "ins_INTEGRATION_TYPE" (code, label, description)
VALUES ('DEVICE_TABLET', 'Device Tablet', 'Device Tablet')
ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ams_driver_login_sessions" (
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    user_id VARCHAR(40) NOT NULL,
    logged_in_time TIMESTAMPTZ NOT NULL,
    logged_out_time TIMESTAMPTZ,

    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_ams_driver_login_sessions_asset_id ON "ams_driver_login_sessions" ("asset_id") WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_ams_driver_login_sessions_client_id ON "ams_driver_login_sessions" ("client_id") WHERE deleted_at IS NULL;

ALTER TABLE "uis_user_clients"
ADD COLUMN IF NOT EXISTS "unencrypted_pin" VARCHAR(20);

COMMIT;
