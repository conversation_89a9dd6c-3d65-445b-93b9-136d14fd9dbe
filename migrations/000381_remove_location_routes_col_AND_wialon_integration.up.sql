BEGIN;

ALTER TABLE ams_location_routes
DROP COLUMN IF EXISTS operation_end_date;

ALTER TABLE ams_weight_bridge_tickets
DROP COLUMN IF EXISTS location_route_id;


UPDATE "uis_CLIENT_PACKAGES" SET "label"='Track Optimax', description='Track Optimax' WHERE code='WIALON_OPTIMAX';
INSERT INTO "uis_PERMISSION_CATEGORIES" (code, title, subtitle, icon, "label", description, display_sequence, client_package_code) VALUES('WIALON_TRACK_OPTIMAX', 'Track Optimax', 'Track Optimax', 'assets.svg', 'Track Optimax', '-', 7, 'WIALON_OPTIMAX');
INSERT INTO "uis_OPTIMAX_PACKAGES" (code, "label", description) VALUES('WIALON_OPTIMAX', 'Track Optimax', '-');

INSERT INTO "uis_PERMISSIONS" (code, user_permission_category_code, "label", description, display_sequence) VALUES('VIEW_WIALON_TRACK_OPTIMAX', 'WIALON_TRACK_OPTIMAX', 'View Track Optimax', 'Users Can View Track Optimax', 0);
INSERT INTO "uis_PERMISSIONS" (code, user_permission_category_code, "label", description, display_sequence) VALUES('EDIT_WIALON_TRACK_OPTIMAX', 'WIALON_TRACK_OPTIMAX', 'Edit Track Optimax', 'Users Can Edit Track Optimax', 1);

ALTER TABLE public.uis_user_clients ADD wialon_account_username varchar(150) NULL;
ALTER TABLE public.uis_user_clients ADD wialon_account_id varchar(100) NULL;
ALTER TABLE public.uis_user_clients ADD is_block_wialon_account boolean DEFAULT false NULL;

ALTER TABLE public.ams_asset_vehicles ADD wialon_unit_id varchar(100) NULL;

COMMIT;